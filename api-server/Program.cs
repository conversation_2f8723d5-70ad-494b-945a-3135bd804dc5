using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using VidCompressor.Data;
using VidCompressor.Services;
using VidCompressor.ApiServer.Middleware;
using VidCompressor.ApiServer.Hubs;
using VidCompressor.Extensions;
using VidCompressor.Configuration;

var builder = WebApplication.CreateBuilder(args);

// Add Google Secret Manager as a configuration source
// This loads secrets directly at startup - no local files needed!
builder.Configuration.AddSecretManager(builder.Configuration);

// Set Firestore emulator environment variable early
var firestoreConfig = builder.Configuration.GetSection("Firestore");
if (firestoreConfig.GetValue<bool>("UseEmulator"))
{
    var emulatorHost = firestoreConfig.GetValue<string>("EmulatorHost");
    if (!string.IsNullOrEmpty(emulatorHost))
    {
        Environment.SetEnvironmentVariable("FIRESTORE_EMULATOR_HOST", emulatorHost);
        Console.WriteLine($"[API-SERVER] Set FIRESTORE_EMULATOR_HOST to: {emulatorHost}");
    }
}

// Add services to the container.
builder.Services.AddFirestore(builder.Configuration);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    var projectId = builder.Configuration["GoogleCloud:ProjectId"] ?? throw new InvalidOperationException("GoogleCloud ProjectId not configured");
    var serviceAccountEmail = $"vidcompressor-app@{projectId}.iam.gserviceaccount.com";

    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        ValidateIssuer = true,
        ValidIssuer = serviceAccountEmail,
        ValidateAudience = true,
        ValidAudience = "vidcompressor-api",
        ValidateLifetime = true,
        ClockSkew = TimeSpan.FromMinutes(5),
        // For Google-signed JWTs, we need to get the public keys from Google
        IssuerSigningKeyResolver = (token, securityToken, kid, parameters) =>
        {
            try
            {
                // Google's public keys for service account signed JWTs
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(10); // Add timeout
                var response = httpClient.GetAsync($"https://www.googleapis.com/service_accounts/v1/jwk/{serviceAccountEmail}").Result;
                if (response.IsSuccessStatusCode)
                {
                    var jwks = response.Content.ReadAsStringAsync().Result;
                    var keySet = new Microsoft.IdentityModel.Tokens.JsonWebKeySet(jwks);
                    return keySet.Keys;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[JWT] Failed to fetch Google public keys: {ex.Message}");
            }

            // Fallback to traditional JWT validation for development/testing
            var jwtSecret = builder.Configuration["Jwt:Secret"];
            if (!string.IsNullOrEmpty(jwtSecret))
            {
                return new[] { new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSecret)) };
            }

            return new List<SecurityKey>();
        }
    };

    // Configure JWT for SignalR
    options.Events = new Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerEvents
    {
        OnMessageReceived = context =>
        {
            var accessToken = context.Request.Query["access_token"];
            var path = context.HttpContext.Request.Path;

            // If the request is for our SignalR hub and we have a token
            if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/notificationHub"))
            {
                context.Token = accessToken;
            }

            return Task.CompletedTask;
        }
    };
});

// Configure Google Cloud settings
builder.Services.Configure<GoogleCloudConfig>(builder.Configuration.GetSection("GoogleCloud"));

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend",
        builder =>
        {
            var allowedOrigins = new List<string> { "http://localhost:3000" }; // Local development

            // Add Firebase Hosting URLs and custom domain for production
            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Production")
            {
                allowedOrigins.Add("https://tranquil-bison-465923-v9.web.app");
                allowedOrigins.Add("https://tranquil-bison-465923-v9.firebaseapp.com");
                allowedOrigins.Add("https://gallerytuner.com");
                allowedOrigins.Add("https://www.gallerytuner.com");
            }

            builder.WithOrigins(allowedOrigins.ToArray())
                   .AllowAnyHeader()
                   .AllowAnyMethod()
                   .AllowCredentials(); // Required for SignalR
        });
});

builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Converters.Add(new System.Text.Json.Serialization.JsonStringEnumConverter());
    });
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddHttpClient();

// Add shared services
builder.Services.AddScoped<GooglePhotosService>();
builder.Services.AddScoped<GoogleCloudStorageService>();
builder.Services.AddScoped<GoogleTranscoderService>();
builder.Services.AddScoped<CloudTasksService>();
builder.Services.AddScoped<CreditsService>();
builder.Services.AddSignalR();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Add global exception handling
app.UseMiddleware<GlobalExceptionMiddleware>();

app.UseHttpsRedirection();

app.UseCors("AllowFrontend");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.MapHub<NotificationHub>("/notificationHub");

app.Run();
