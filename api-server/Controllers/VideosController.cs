using Google.Apis.Auth.OAuth2;
using Google.Apis.PhotosLibrary.v1;
using Google.Apis.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using VidCompressor.Models;
using VidCompressor.Services;
using VidCompressor.Repositories;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class VideosController : ControllerBase
{
    private readonly ICompressionJobRepository _compressionJobRepository;
    private readonly IUserRepository _userRepository;
    private readonly IConfiguration _configuration; // Inject IConfiguration for ClientId/Secret
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly GoogleCloudStorageService _storageService;
    private readonly GoogleTranscoderService _transcoderService;
    private readonly CloudTasksService _cloudTasksService;
    private readonly CreditsService _creditsService;

    public VideosController(
        ICompressionJobRepository compressionJobRepository,
        IUserRepository userRepository,
        IConfiguration configuration,
        IHttpClientFactory httpClientFactory,
        GoogleCloudStorageService storageService,
        GoogleTranscoderService transcoderService,
        CloudTasksService cloudTasksService,
        CreditsService creditsService)
    {
        _compressionJobRepository = compressionJobRepository;
        _userRepository = userRepository;
        _configuration = configuration;
        _httpClientFactory = httpClientFactory;
        _storageService = storageService;
        _transcoderService = transcoderService;
        _cloudTasksService = cloudTasksService;
        _creditsService = creditsService;
    }

    /// <summary>
    /// Initiates the Google Photos Picker API flow for media selection (photos and videos).
    ///
    /// This endpoint follows the Google Photos Picker API workflow:
    /// 1. Validates the user's OAuth 2.0 access token for Google Photos API access
    /// 2. Creates a new picker session via the Google Photos Picker API
    /// 3. Returns a pickerUri that the client should direct the user to for media selection
    ///
    /// After receiving the pickerUri, the client should:
    /// 1. Direct the user to the pickerUri (opens Google Photos app for selection)
    /// 2. Poll GET /api/videos/session/{sessionId}/status until mediaItemsSet becomes true
    /// 3. Call GET /api/videos/session/{sessionId}/videos to retrieve selected media files
    /// 4. Optionally call DELETE /api/videos/session/{sessionId} to clean up the session
    ///
    /// Returns:
    /// - 200 OK: Session created successfully with sessionId and pickerUri
    /// - 401 Unauthorized: No valid Google access token found
    /// - 403 Forbidden: User needs to re-authenticate with updated permissions
    /// - 500 Internal Server Error: Failed to create picker session
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetVideos()
    {
        var accessToken = await GetCurrentGoogleAccessTokenAsync();
        if (accessToken == null)
        {
            return Unauthorized(new { message = "Google access token not found or expired" });
        }

        try
        {
            // Create a new picker session
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var sessionRequest = new
            {
                // Empty body for session creation
            };

            var sessionResponse = await httpClient.PostAsJsonAsync("https://photospicker.googleapis.com/v1/sessions", sessionRequest);

            if (!sessionResponse.IsSuccessStatusCode)
            {
                var errorContent = await sessionResponse.Content.ReadAsStringAsync();

                // Check if this is a scope permission error
                if (sessionResponse.StatusCode == System.Net.HttpStatusCode.Forbidden &&
                    errorContent.Contains("ACCESS_TOKEN_SCOPE_INSUFFICIENT"))
                {
                    return StatusCode(403, new {
                        message = "Insufficient permissions. Please re-authenticate to access Google Photos.",
                        error = "SCOPE_INSUFFICIENT",
                        requiresReauth = true,
                        authUrl = GetReauthUrl()
                    });
                }

                return StatusCode(500, new { message = "Failed to create picker session", error = errorContent });
            }

            var sessionContent = await sessionResponse.Content.ReadAsStringAsync();
            var sessionData = JsonSerializer.Deserialize<PickerSessionResponse>(sessionContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (sessionData?.PickerUri == null)
            {
                return StatusCode(500, new { message = "Invalid session response from Google Photos Picker API" });
            }

            return Ok(new {
                sessionId = sessionData.Id,
                pickerUri = sessionData.PickerUri,
                message = "Picker session created. Direct user to pickerUri to select videos."
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while creating picker session", error = ex.Message });
        }
    }

    [HttpGet("session/{sessionId}/status")]
    public async Task<IActionResult> GetSessionStatus(string sessionId)
    {
        var accessToken = await GetCurrentGoogleAccessTokenAsync();
        if (accessToken == null)
        {
            return Unauthorized(new { message = "Google access token not found or expired" });
        }

        try
        {
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var sessionResponse = await httpClient.GetAsync($"https://photospicker.googleapis.com/v1/sessions/{sessionId}");

            if (!sessionResponse.IsSuccessStatusCode)
            {
                var errorContent = await sessionResponse.Content.ReadAsStringAsync();
                return StatusCode(500, new { message = "Failed to get session status", error = errorContent });
            }

            var sessionContent = await sessionResponse.Content.ReadAsStringAsync();
            var sessionData = JsonSerializer.Deserialize<PickerSessionResponse>(sessionContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return Ok(sessionData);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while checking session status", error = ex.Message });
        }
    }

    /// <summary>
    /// Retrieves all selected media items (photos and videos) from a Google Photos Picker session.
    /// This endpoint should be called after the session status shows mediaItemsSet = true.
    ///
    /// Returns:
    /// - mediaItems: Array of all selected media items with metadata
    /// - videoCount: Number of videos in the selection
    /// - photoCount: Number of photos in the selection
    /// - totalSelected: Total number of items selected
    /// - nextPageToken: Token for pagination if more items exist
    /// </summary>
    [HttpGet("session/{sessionId}/videos")]
    public async Task<IActionResult> GetSelectedVideos(string sessionId, [FromQuery] string? pageToken = null, [FromQuery] int pageSize = 50)
    {
        var accessToken = await GetCurrentGoogleAccessTokenAsync();
        if (accessToken == null)
        {
            return Unauthorized(new { message = "Google access token not found or expired" });
        }

        try
        {
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            // Build query parameters
            var queryParams = new List<string>
            {
                $"sessionId={sessionId}",
                $"pageSize={pageSize}"
            };

            if (!string.IsNullOrEmpty(pageToken))
            {
                queryParams.Add($"pageToken={pageToken}");
            }

            var queryString = string.Join("&", queryParams);
            var mediaItemsResponse = await httpClient.GetAsync($"https://photospicker.googleapis.com/v1/mediaItems?{queryString}");

            if (!mediaItemsResponse.IsSuccessStatusCode)
            {
                var errorContent = await mediaItemsResponse.Content.ReadAsStringAsync();
                return StatusCode(500, new { message = "Failed to get selected media items", error = errorContent });
            }

            var mediaItemsContent = await mediaItemsResponse.Content.ReadAsStringAsync();
            Console.WriteLine($"Raw media items response: {mediaItemsContent}");

            var mediaItemsData = JsonSerializer.Deserialize<ListMediaItemsResponse>(mediaItemsContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (mediaItemsData?.MediaItems == null)
            {
                Console.WriteLine("No media items found in response");
                return Ok(new { videos = new List<PickedMediaItem>(), nextPageToken = (string?)null, debug = "No media items in response" });
            }

            Console.WriteLine($"Total media items received: {mediaItemsData.MediaItems.Count}");

            // Log all media items for debugging
            foreach (var item in mediaItemsData.MediaItems)
            {
                Console.WriteLine($"Media item: ID={item.Id}, Type={item.Type}, MimeType={item.MediaFile?.MimeType}, Filename={item.MediaFile?.Filename}");
            }

            // Transform the data to a flatter structure for the frontend
            var transformedItems = mediaItemsData.MediaItems.Select(item => new
            {
                id = item.Id,
                filename = item.MediaFile?.Filename ?? "Unknown",
                mimeType = item.MediaFile?.MimeType ?? "unknown/unknown",
                baseUrl = item.MediaFile?.BaseUrl ?? "",
                googlePhotosUrl = item.ProductUrl,
                type = item.Type,
                createTime = item.CreateTime,
                mediaMetadata = new
                {
                    width = item.MediaFile?.MediaFileMetadata?.Width.ToString() ?? "0",
                    height = item.MediaFile?.MediaFileMetadata?.Height.ToString() ?? "0",
                    cameraMake = item.MediaFile?.MediaFileMetadata?.CameraMake ?? "",
                    cameraModel = item.MediaFile?.MediaFileMetadata?.CameraModel ?? "",
                    video = item.MediaFile?.MediaFileMetadata?.VideoMetadata != null ? new
                    {
                        fps = item.MediaFile.MediaFileMetadata.VideoMetadata.Fps,
                        processingStatus = item.MediaFile.MediaFileMetadata.VideoMetadata.ProcessingStatus
                    } : null,
                    photo = item.MediaFile?.MediaFileMetadata?.PhotoMetadata != null ? new
                    {
                        focalLength = item.MediaFile.MediaFileMetadata.PhotoMetadata.FocalLength,
                        apertureFNumber = item.MediaFile.MediaFileMetadata.PhotoMetadata.ApertureFNumber,
                        isoEquivalent = item.MediaFile.MediaFileMetadata.PhotoMetadata.IsoEquivalent,
                        exposureTime = item.MediaFile.MediaFileMetadata.PhotoMetadata.ExposureTime
                    } : null
                }
            }).ToList();

            var videoCount = transformedItems.Count(item => item.mimeType.StartsWith("video/", StringComparison.OrdinalIgnoreCase));
            var photoCount = transformedItems.Count(item => item.mimeType.StartsWith("image/", StringComparison.OrdinalIgnoreCase));

            Console.WriteLine($"Total media items: {transformedItems.Count} (Videos: {videoCount}, Photos: {photoCount})");

            return Ok(new {
                mediaItems = transformedItems,
                nextPageToken = mediaItemsData.NextPageToken,
                totalSelected = mediaItemsData.MediaItems.Count,
                videoCount = videoCount,
                photoCount = photoCount,
                debug = new {
                    rawResponseLength = mediaItemsContent.Length,
                    totalItems = mediaItemsData.MediaItems.Count,
                    mimeTypes = transformedItems.Select(i => i.mimeType).ToList()
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving selected videos", error = ex.Message });
        }
    }

    [HttpDelete("session/{sessionId}")]
    public async Task<IActionResult> DeleteSession(string sessionId)
    {
        var accessToken = await GetCurrentGoogleAccessTokenAsync();
        if (accessToken == null)
        {
            return Unauthorized(new { message = "Google access token not found or expired" });
        }

        try
        {
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var deleteResponse = await httpClient.DeleteAsync($"https://photospicker.googleapis.com/v1/sessions/{sessionId}");

            if (!deleteResponse.IsSuccessStatusCode)
            {
                var errorContent = await deleteResponse.Content.ReadAsStringAsync();
                return StatusCode(500, new { message = "Failed to delete session", error = errorContent });
            }

            return Ok(new { message = "Session deleted successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while deleting session", error = ex.Message });
        }
    }

    [HttpGet("{mediaItemId}/info")]
    public async Task<IActionResult> GetVideoInfo(string mediaItemId)
    {
        var accessToken = await GetCurrentGoogleAccessTokenAsync();
        if (accessToken == null)
        {
            return Unauthorized(new { message = "Google access token not found or expired" });
        }

        try
        {
            // Create the service on-demand with the user's token
            var photosService = CreatePhotosLibraryService(accessToken);
            var videoInfo = await photosService.MediaItems.Get(mediaItemId).ExecuteAsync();
            return Ok(videoInfo);
        }
        catch (Exception ex)
        {
            // Handle potential API errors (e.g., token revoked, permissions denied)
            return StatusCode(500, new { message = "An error occurred while communicating with Google Photos.", error = ex.Message });
        }
    }

    [HttpGet("{mediaItemId}/preview")]
    [AllowAnonymous]
    public async Task<IActionResult> GetMediaPreview(string mediaItemId, [FromQuery] int width = 200, [FromQuery] int height = 200, [FromQuery] bool crop = false, [FromQuery] string? token = null, [FromQuery] string? baseUrl = null)
    {
        Console.WriteLine($"[Preview] Starting preview request for mediaItemId: {mediaItemId}");

        // Try to get token from query parameter first (for img src), then from Authorization header
        string? jwtToken = token;
        if (string.IsNullOrEmpty(jwtToken))
        {
            // Fallback to Authorization header
            var authHeader = Request.Headers.Authorization.FirstOrDefault();
            if (authHeader?.StartsWith("Bearer ") == true)
            {
                jwtToken = authHeader.Substring("Bearer ".Length).Trim();
            }
        }

        Console.WriteLine($"[Preview] JWT Token present: {!string.IsNullOrEmpty(jwtToken)}");
        if (string.IsNullOrEmpty(jwtToken))
        {
            Console.WriteLine("[Preview] No JWT token found");
            return Unauthorized(new { message = "Authentication token required" });
        }

        // Validate the JWT token and get user
        var userId = await ValidateJwtTokenAndGetUserId(jwtToken);
        Console.WriteLine($"[Preview] User ID from token: {userId}");
        if (string.IsNullOrEmpty(userId))
        {
            Console.WriteLine("[Preview] JWT token validation failed");
            return Unauthorized(new { message = "Invalid or expired token" });
        }

        var accessToken = await GetGoogleAccessTokenForUser(userId);
        Console.WriteLine($"[Preview] Google access token present: {!string.IsNullOrEmpty(accessToken)}");
        if (accessToken == null)
        {
            Console.WriteLine("[Preview] No Google access token found");
            return Unauthorized(new { message = "Google access token not found or expired" });
        }

        try
        {
            string mediaBaseUrl;

            // If baseUrl is provided from the frontend (from Picker API), use it directly
            if (!string.IsNullOrEmpty(baseUrl))
            {
                Console.WriteLine($"[Preview] Using provided baseUrl from Picker API");
                mediaBaseUrl = baseUrl;
            }
            else
            {
                Console.WriteLine($"[Preview] No baseUrl provided, falling back to Library API (may fail for user-selected media)");
                // Fallback to Library API (this will likely fail for user-selected media due to scope restrictions)
                var photosService = CreatePhotosLibraryService(accessToken);
                Console.WriteLine($"[Preview] Attempting to get media item: {mediaItemId}");
                var mediaItem = await photosService.MediaItems.Get(mediaItemId).ExecuteAsync();

                if (mediaItem?.BaseUrl == null)
                {
                    return NotFound(new { message = "Media item not found or BaseUrl is missing." });
                }
                mediaBaseUrl = mediaItem.BaseUrl;
            }

            // Construct the preview URL using the BaseUrl
            string previewUrl = $"{mediaBaseUrl}=w{width}-h{height}";
            if (crop)
            {
                previewUrl += "-c";
            }

            Console.WriteLine($"[Preview] Requesting preview from: {previewUrl}");

            // Make an authenticated HTTP request to Google Photos for the preview image
            var httpClient = _httpClientFactory.CreateClient(); // Use factory for better management
            httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var imageResponse = await httpClient.GetAsync(previewUrl);

            if (!imageResponse.IsSuccessStatusCode)
            {
                var errorContent = await imageResponse.Content.ReadAsStringAsync();
                Console.WriteLine($"Error fetching preview from Google Photos: {imageResponse.StatusCode} - {errorContent}");

                if (imageResponse.StatusCode == System.Net.HttpStatusCode.Forbidden || imageResponse.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    return StatusCode(403, new {
                        message = "Insufficient permissions to access media preview. The access token might be invalid or expired for this BaseUrl.",
                        error = "PREVIEW_ACCESS_FORBIDDEN",
                        requiresReauth = true,
                        authUrl = GetReauthUrl(),
                        suggestion = "Try re-selecting media through Google Photos Picker to get fresh access tokens."
                    });
                }

                return StatusCode((int)imageResponse.StatusCode, new {
                    message = "Failed to fetch media preview from Google Photos.",
                    error = errorContent
                });
            }

            // Stream the image content back to the frontend
            var imageStream = await imageResponse.Content.ReadAsStreamAsync();
            var contentType = imageResponse.Content.Headers.ContentType?.ToString() ?? "image/jpeg";

            return File(imageStream, contentType);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Preview] Exception occurred: {ex.Message}");
            return StatusCode(500, new { message = "An error occurred while retrieving media preview", error = ex.Message });
        }
    }

    [HttpGet("{mediaItemId}/download")]
    public async Task<IActionResult> DownloadVideo(string mediaItemId, [FromQuery] string? baseUrl = null)
    {
        var accessToken = await GetCurrentGoogleAccessTokenAsync();
        if (accessToken == null)
        {
            return Unauthorized(new { message = "Google access token not found or expired" });
        }

        try
        {
            string mediaBaseUrl;

            // If baseUrl is provided from the frontend (from Picker API), use it directly
            if (!string.IsNullOrEmpty(baseUrl))
            {
                Console.WriteLine($"[Download] Using provided baseUrl from Picker API");
                mediaBaseUrl = baseUrl;
            }
            else
            {
                Console.WriteLine($"[Download] No baseUrl provided, falling back to Library API (may fail for user-selected media)");
                // Fallback to Library API (this will likely fail for user-selected media due to scope restrictions)
                var photosService = CreatePhotosLibraryService(accessToken);
                var mediaItem = await photosService.MediaItems.Get(mediaItemId).ExecuteAsync();
                if (mediaItem?.BaseUrl == null)
                {
                    return NotFound(new { message = "Video not found or is not available for download." });
                }
                mediaBaseUrl = mediaItem.BaseUrl;
            }

            // The download URL is the baseUrl with the '=d' parameter
            var downloadUrl = $"{mediaBaseUrl}=d";

            // Use HttpClient to stream the file with authentication
            var httpClient = _httpClientFactory.CreateClient();
            httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var videoStream = await httpClient.GetStreamAsync(downloadUrl);

            return File(videoStream, "video/mp4", "video.mp4");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Download] Exception occurred: {ex.Message}");
            return StatusCode(500, new { message = "An error occurred while downloading media", error = ex.Message });
        }
    }

    /// <summary>
    /// Initiates video compression using Google Transcoder API
    /// </summary>
    [HttpPost("{mediaItemId}/compress")]
    public async Task<IActionResult> CompressVideo(string mediaItemId, [FromBody] CompressionJobRequest request)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        try
        {
            // Calculate credit cost for this operation
            double? durationMinutes = null;
            if (request.MediaType == MediaType.Video)
            {
                // Default to 1 minute for videos since duration parsing from metadata is complex
                durationMinutes = 1.0;
            }

            var creditCost = await _creditsService.CalculateCreditCostAsync(
                request.MediaType,
                request.Quality,
                durationMinutes);

            // Check if user has sufficient credits
            var hasSufficientCredits = await _creditsService.HasSufficientCreditsAsync(userId, creditCost);
            if (!hasSufficientCredits)
            {
                var currentBalance = await _creditsService.GetUserCreditsAsync(userId);
                return BadRequest(new {
                    error = "Insufficient credits",
                    required = creditCost,
                    current = currentBalance,
                    shortfall = creditCost - currentBalance
                });
            }

            // Create compression job record
            var compressionJob = new CompressionJob
            {
                UserId = userId,
                MediaItemId = mediaItemId,
                MediaType = request.MediaType,
                Quality = request.Quality,
                UploadToGooglePhotos = request.UploadToGooglePhotos,
                BaseUrl = request.BaseUrl,
                OriginalWidth = request.OriginalWidth,
                OriginalHeight = request.OriginalHeight,
                OriginalFilename = request.Filename,
                GooglePhotosUrl = request.GooglePhotosUrl,
                CreditsUsed = creditCost,
                Status = CompressionJobStatus.Queued
            };

            await _compressionJobRepository.AddAsync(compressionJob);

            // Deduct credits from user account
            var mediaTypeText = request.MediaType == MediaType.Photo ? "photo" : "video";
            var deductionSuccess = await _creditsService.DeductCreditsAsync(
                userId,
                creditCost,
                $"{char.ToUpper(mediaTypeText[0])}{mediaTypeText[1..]} compression ({request.Quality} quality)",
                compressionJob.Id);

            if (!deductionSuccess)
            {
                // If credit deduction fails, mark job as failed and don't process
                compressionJob.Status = CompressionJobStatus.Failed;
                compressionJob.ErrorMessage = "Failed to deduct credits";
                await _compressionJobRepository.UpdateAsync(compressionJob);

                return BadRequest(new { error = "Failed to deduct credits for compression" });
            }

            // Send initial status update via SignalR
            try
            {
                var hubContext = HttpContext.RequestServices.GetRequiredService<IHubContext<VidCompressor.ApiServer.Hubs.NotificationHub>>();
                await hubContext.Clients.All.SendAsync("CompressionStatusUpdate", new
                {
                    jobId = compressionJob.Id,
                    mediaItemId = compressionJob.MediaItemId,
                    status = "Queued",
                    message = "Job queued for processing",
                    progress = 0,
                    userId = userId
                });
                Console.WriteLine($"Sent initial SignalR update for job {compressionJob.Id}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to send initial SignalR update: {ex.Message}");
            }

            // Queue the job for background processing
            await _cloudTasksService.EnqueueCompressionJobAsync(compressionJob.Id);

            return Ok(new CompressionJobResponse
            {
                JobId = compressionJob.Id,
                Status = compressionJob.Status.ToString(),
                Message = "Video compression job has been queued",
                CreatedAt = compressionJob.CreatedAt
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to queue compression job", details = ex.Message });
        }
    }

    /// <summary>
    /// Gets the status of a compression job
    /// </summary>
    [HttpGet("compression-jobs/{jobId}/status")]
    public async Task<IActionResult> GetCompressionJobStatus(string jobId)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        try
        {
            var job = await _compressionJobRepository.GetByIdAsync(jobId);

            if (job != null && job.UserId != userId)
            {
                job = null; // User doesn't own this job
            }

            if (job == null)
            {
                return NotFound(new { error = "Compression job not found" });
            }

            return Ok(new CompressionJobResponse
            {
                JobId = job.Id,
                Status = job.Status.ToString(),
                Message = GetStatusMessage(job.Status),
                CreatedAt = job.CreatedAt,
                CompletedAt = job.CompletedAt,
                CompressionRatio = job.CompressionRatio,
                ErrorMessage = job.ErrorMessage
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to get job status", details = ex.Message });
        }
    }

    /// <summary>
    /// Gets all compression jobs for the current user
    /// </summary>
    [HttpGet("compression-jobs")]
    public async Task<IActionResult> GetCompressionJobs([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        try
        {
            var allJobs = await _compressionJobRepository.GetByUserIdAsync(userId);
            var totalJobs = allJobs.Count();

            var jobs = allJobs
                .OrderByDescending(j => j.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(j => new CompressionJobResponse
                {
                    JobId = j.Id,
                    Status = j.Status.ToString(),
                    Message = GetStatusMessage(j.Status),
                    CreatedAt = j.CreatedAt,
                    CompletedAt = j.CompletedAt,
                    CompressionRatio = j.CompressionRatio,
                    ErrorMessage = j.ErrorMessage
                })
                .ToList();

            return Ok(new
            {
                jobs,
                totalJobs,
                page,
                pageSize,
                totalPages = (int)Math.Ceiling((double)totalJobs / pageSize)
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to get compression jobs", details = ex.Message });
        }
    }

    /// <summary>
    /// Cancels a compression job
    /// </summary>
    [HttpPost("compression-jobs/{jobId}/cancel")]
    public async Task<IActionResult> CancelCompressionJob(string jobId)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        try
        {
            var job = await _compressionJobRepository.GetByIdAsync(jobId);

            if (job == null || job.UserId != userId)
            {
                return NotFound(new { error = "Compression job not found" });
            }



            if (job.Status == CompressionJobStatus.Completed || job.Status == CompressionJobStatus.Failed)
            {
                return BadRequest(new { error = "Cannot cancel a completed or failed job" });
            }

            // Cancel the transcoder job if it exists
            if (!string.IsNullOrEmpty(job.TranscoderJobName))
            {
                try
                {
                    await _transcoderService.CancelJobAsync(job.TranscoderJobName);
                }
                catch (Exception ex)
                {
                    // Log the error but continue with marking the job as cancelled
                    Console.WriteLine($"Failed to cancel transcoder job: {ex.Message}");
                }
            }

            job.Status = CompressionJobStatus.Cancelled;
            await _compressionJobRepository.UpdateAsync(job);

            return Ok(new { message = "Compression job cancelled successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to cancel compression job", details = ex.Message });
        }
    }

    private static string GetStatusMessage(CompressionJobStatus status)
    {
        return status switch
        {
            CompressionJobStatus.Queued => "Job is queued for processing",
            CompressionJobStatus.DownloadingFromGooglePhotos => "Downloading video from Google Photos",
            CompressionJobStatus.UploadingToStorage => "Uploading video to cloud storage",
            CompressionJobStatus.TranscodingInProgress => "Compressing video",
            CompressionJobStatus.DownloadingFromStorage => "Downloading compressed video",
            CompressionJobStatus.UploadingToGooglePhotos => "Uploading compressed video to Google Photos",
            CompressionJobStatus.Completed => "Compression completed",
            CompressionJobStatus.Failed => "Compression failed",
            CompressionJobStatus.Cancelled => "Compression was cancelled",
            _ => "Unknown status"
        };
    }

    // Helper method to create the Photos service
    private PhotosLibraryService CreatePhotosLibraryService(string accessToken)
    {
        var credential = GoogleCredential.FromAccessToken(accessToken);
        return new PhotosLibraryService(new BaseClientService.Initializer
        {
            HttpClientInitializer = credential,
            ApplicationName = "VidCompressor"
        });
    }



    // Your existing token retrieval logic
    private async Task<string?> GetCurrentGoogleAccessTokenAsync()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return null;
        }

        // Note: Consider caching this result for a short period to reduce DB hits
        var user = await _userRepository.GetByIdAsync(userId);

        if (user == null || string.IsNullOrEmpty(user.GoogleAccessToken))
        {
            return null;
        }

        if (user.GoogleTokenExpiry.HasValue && user.GoogleTokenExpiry.Value <= DateTime.UtcNow)
        {
            // Try to refresh the access token using the refresh token
            var refreshedToken = await RefreshGoogleAccessToken(user);
            return refreshedToken;
        }

        return user.GoogleAccessToken;
    }

    private async Task<string?> RefreshGoogleAccessToken(User user)
    {
        if (string.IsNullOrEmpty(user.GoogleRefreshToken))
        {
            return null; // User needs to re-authenticate
        }

        try
        {
            var httpClient = new HttpClient();
            var tokenRequest = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("client_id", _configuration["Google:ClientId"] ?? ""),
                new KeyValuePair<string, string>("client_secret", _configuration["Google:ClientSecret"] ?? ""),
                new KeyValuePair<string, string>("refresh_token", user.GoogleRefreshToken),
                new KeyValuePair<string, string>("grant_type", "refresh_token")
            });

            var tokenResponse = await httpClient.PostAsync("https://oauth2.googleapis.com/token", tokenRequest);
            var tokenContent = await tokenResponse.Content.ReadAsStringAsync();

            if (!tokenResponse.IsSuccessStatusCode)
            {
                // Invalidate the stored refresh token if it consistently fails
                user.GoogleRefreshToken = null;
                await _userRepository.UpdateAsync(user);
                return null;
            }

            var tokenData = JsonSerializer.Deserialize<GoogleTokenResponse>(tokenContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (tokenData != null && !string.IsNullOrEmpty(tokenData.AccessToken))
            {
                user.GoogleAccessToken = tokenData.AccessToken;
                user.GoogleTokenExpiry = DateTime.UtcNow.AddSeconds(tokenData.ExpiresIn);
                // The refresh token is often not returned on refresh, but if it is, update it.
                if (!string.IsNullOrEmpty(tokenData.RefreshToken))
                {
                    user.GoogleRefreshToken = tokenData.RefreshToken;
                }
                await _userRepository.UpdateAsync(user);
                return tokenData.AccessToken;
            }
            return null;
        }
        catch (Exception)
        {
            return null;
        }
    }

    private string GetReauthUrl()
    {
        var clientId = _configuration["Google:ClientId"];
        var redirectUri = _configuration["Google:RedirectUri"] ?? throw new InvalidOperationException("Google RedirectUri not configured");
        var scopes = "openid email profile https://www.googleapis.com/auth/photoslibrary.appendonly https://www.googleapis.com/auth/photospicker.mediaitems.readonly";

        var authUrl = $"https://accounts.google.com/o/oauth2/v2/auth?" +
                     $"client_id={clientId}&" +
                     $"redirect_uri={Uri.EscapeDataString(redirectUri)}&" +
                     $"scope={Uri.EscapeDataString(scopes)}&" +
                     $"response_type=code&" +
                     $"access_type=offline&" +
                     $"prompt=consent";

        return authUrl;
    }

    private async Task<string?> ValidateJwtTokenAndGetUserId(string jwtToken)
    {
        try
        {
            Console.WriteLine($"[JWT] Validating token: {jwtToken.Substring(0, Math.Min(50, jwtToken.Length))}...");
            var tokenHandler = new JwtSecurityTokenHandler();

            // First, try to validate as a Google-signed JWT
            var projectId = _configuration["GoogleCloud:ProjectId"];
            if (!string.IsNullOrEmpty(projectId))
            {
                var serviceAccountEmail = $"vidcompressor-app@{projectId}.iam.gserviceaccount.com";

                try
                {
                    // Get Google's public keys for the service account
                    using var httpClient = new HttpClient();
                    var response = await httpClient.GetAsync($"https://www.googleapis.com/service_accounts/v1/jwk/{serviceAccountEmail}");

                    if (response.IsSuccessStatusCode)
                    {
                        var jwks = await response.Content.ReadAsStringAsync();
                        var keySet = new Microsoft.IdentityModel.Tokens.JsonWebKeySet(jwks);

                        var validationParameters = new TokenValidationParameters
                        {
                            ValidateIssuerSigningKey = true,
                            IssuerSigningKeys = keySet.Keys,
                            ValidateIssuer = true,
                            ValidIssuer = serviceAccountEmail,
                            ValidateAudience = true,
                            ValidAudience = "vidcompressor-api",
                            ValidateLifetime = true,
                            ClockSkew = TimeSpan.FromMinutes(5)
                        };

                        tokenHandler.ValidateToken(jwtToken, validationParameters, out SecurityToken validatedToken);
                        var jwtSecurityToken = (JwtSecurityToken)validatedToken;

                        var userId = jwtSecurityToken.Claims.FirstOrDefault(x => x.Type == "sub")?.Value;
                        if (!string.IsNullOrEmpty(userId))
                        {
                            Console.WriteLine($"[JWT] Google-signed token validation successful, userId: {userId}");
                            return userId;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[JWT] Google-signed token validation failed: {ex.Message}");
                    // Only Google-signed JWTs are supported
                    throw new UnauthorizedAccessException("Invalid JWT token - only Google-signed JWTs are accepted", ex);
                }
            }

            // If we reach here, no valid Google project configuration was found
            throw new UnauthorizedAccessException("Invalid JWT token - Google project configuration not found");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[JWT] Token validation failed: {ex.Message}");
            return null;
        }
    }

    private async Task<string?> GetGoogleAccessTokenForUser(string userId)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return null;
            }

            // Check if token is expired and refresh if needed
            if (user.GoogleTokenExpiry <= DateTime.UtcNow && !string.IsNullOrEmpty(user.GoogleRefreshToken))
            {
                var refreshedToken = await RefreshGoogleAccessToken(user);
                if (refreshedToken != null)
                {
                    user.GoogleAccessToken = refreshedToken;
                    user.GoogleTokenExpiry = DateTime.UtcNow.AddSeconds(3600); // Default 1 hour expiry
                    await _userRepository.UpdateAsync(user);
                    return refreshedToken;
                }
            }

            return user.GoogleAccessToken;
        }
        catch
        {
            return null;
        }
    }
}

